package com.tabbyml.intellijtabby.utils

import com.intellij.openapi.diagnostic.Logger
import java.util.concurrent.CompletableFuture

object CompletableFutureUtils {
  
  /**
   * Safely completes a CompletableFuture, handling any exceptions that might occur
   * during completion (e.g., when LSP connection is broken).
   */
  fun <T> CompletableFuture<T>.safeComplete(value: T, logger: Logger? = null) {
    try {
      if (!isDone) {
        complete(value)
      }
    } catch (e: Exception) {
      logger?.debug("Failed to complete future safely", e)
      // Ignore the error - future might be cancelled or completed already
    }
  }

  /**
   * Wraps a callback to handle exceptions safely and complete the future with fallback value.
   */
  fun <T> CompletableFuture<T>.safeCallback(fallbackValue: T, logger: Logger? = null, callback: () -> Unit) {
    try {
      callback()
    } catch (e: Exception) {
      logger?.debug("Exception in callback, using fallback", e)
      safeComplete(fallbackValue, logger)
    }
  }

  /**
   * Checks if the future is in a state that allows safe completion.
   */
  fun <T> CompletableFuture<T>.canSafelyComplete(): <PERSON><PERSON><PERSON> {
    return !isDone && !isCancelled
  }
}